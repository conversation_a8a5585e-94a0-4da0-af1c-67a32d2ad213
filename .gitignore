# ⚡ NODE.JS
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.env
.env.local
.env.*.local

# ⚡ LOGS Y ERRORES
logs
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
lerna-debug.log*
.log
.pids

# ⚡ DIRECTORIOS DE COMPILACIÓN / BUILD
dist/
build/
.cache/
.temp/

# ⚡ ARCHIVOS DEL EDITOR
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# ⚡ DEPENDENCIAS DEL FRONTEND (React o Vue)
frontend/node_modules/
frontend/build/
frontend/.cache/

# ⚡ ARCHIVOS DEL SISTEMA OPERATIVO
.DS_Store
Thumbs.db

# ⚡ SEGURIDAD - ARCHIVOS SENSIBLES
backend/.env
backend/.env.local
backend/.env.production
*.db
*.sqlite
*.sqlite3
backend/*.db
backend/*.sqlite
backend/*.sqlite3

# ⚡ UPLOADS Y ARCHIVOS TEMPORALES
backend/uploads/*
!backend/uploads/.gitkeep
tmp/
temp/

# ⚡ VERCEL
.vercel

# ⚡ SEGURIDAD - ARCHIVOS SENSIBLES
backend/.env
backend/.env.local
backend/.env.production
*.db
*.sqlite
*.sqlite3
backend/*.db
backend/*.sqlite
backend/*.sqlite3

# ⚡ UPLOADS Y ARCHIVOS TEMPORALES
backend/uploads/*
!backend/uploads/.gitkeep
tmp/
temp/

# ⚡ VERCEL
.vercel

# ⚡ SEGURIDAD - ARCHIVOS SENSIBLES
backend/.env
backend/.env.local
backend/.env.production
*.db
*.sqlite
*.sqlite3
backend/*.db
backend/*.sqlite
backend/*.sqlite3

# ⚡ UPLOADS Y ARCHIVOS TEMPORALES
backend/uploads/*
!backend/uploads/.gitkeep
tmp/
temp/

# ⚡ VERCEL
.vercel


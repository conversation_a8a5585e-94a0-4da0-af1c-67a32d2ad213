{"name": "gym-backend", "version": "1.0.0", "description": "Backend local para gimnasio con SQLite", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "sqlite3": "^5.1.6"}, "devDependencies": {"concurrently": "^9.1.2", "nodemon": "^3.0.1"}, "keywords": ["gym", "sqlite", "express", "api"], "author": "", "license": "ISC"}
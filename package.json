{"name": "gimnasio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "backend": "cd backend && npm start", "backend:dev": "cd backend && npm run dev", "start": "concurrently \"npm run backend\" \"npm run dev\"", "start:dev": "concurrently \"npm run backend:dev\" \"npm run dev\"", "vercel-build": "npm run build && cd api && npm install && cd ../backend && npm install && cp -r node_modules ../api/"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.5", "react": "^19.0.0", "react-calendar": "^5.1.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.8.0", "concurrently": "^9.1.2", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}
name: Keep Supabase Alive

on:
  schedule:
    # Ejecutar cada 6 horas (0, 6, 12, 18 UTC)
    - cron: '0 */6 * * *'
  workflow_dispatch: # Permite ejecutar manualmente

jobs:
  keep-alive:
    runs-on: ubuntu-latest
    
    steps:
    - name: Ping Supabase
      run: |
        echo "🔄 Pinging Supabase to keep it alive..."
        
        response=$(curl -s -w "%{http_code}" \
          -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndzbnR1dWd4ZHp5bnBzbGJyZmR6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzNjgxMDAsImV4cCI6MjA2MDk0NDEwMH0.MSD584rXn9x9LkkNk1ITmRJYhppkfPbXqjCjOA0kZOk" \
          -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndzbnR1dWd4ZHp5bnBzbGJyZmR6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUzNjgxMDAsImV4cCI6MjA2MDk0NDEwMH0.MSD584rXn9x9LkkNk1ITmRJYhppkfPbXqjCjOA0kZOk" \
          "https://wsntuugxdzynpslbrfdz.supabase.co/rest/v1/users?select=count&limit=1")
        
        http_code="${response: -3}"
        body="${response%???}"
        
        if [ "$http_code" = "200" ]; then
          echo "✅ Success! Supabase is alive"
          echo "📊 Response: $body"
          echo "🕐 Timestamp: $(date -u)"
        else
          echo "❌ Error! HTTP Code: $http_code"
          echo "📄 Response: $body"
          exit 1
        fi

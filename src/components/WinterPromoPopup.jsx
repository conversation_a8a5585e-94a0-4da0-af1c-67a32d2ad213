import React, { useState, useEffect } from 'react';
import './WinterPromoPopup.css';

const WinterPromoPopup = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Mostrar el popup después de un pequeño delay para mejor experiencia
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const closePopup = () => {
    setIsVisible(false);
  };

  // Manejar el cierre con la tecla X
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'x' || event.key === 'X') {
        closePopup();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyPress);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="winter-popup-overlay">
      <div className="winter-popup-container">
        <button className="winter-popup-close" onClick={closePopup}>
          ×
        </button>
        
        <div className="winter-popup-content">
          <div className="winter-popup-header">
            <h1 className="winter-popup-title">
              ❄️ PROMOCIÓN DE INVIERNO ❄️
            </h1>
            <div className="winter-popup-subtitle">
              ¡Mantente en forma durante el invierno!
            </div>
          </div>

          <div className="winter-popup-body">
            <div className="winter-popup-offer">
              <div className="winter-popup-discount">
                <span className="discount-percentage">50%</span>
                <span className="discount-text">DESCUENTO</span>
              </div>
              
              <div className="winter-popup-details">
                <h3>🔥 OFERTA ESPECIAL DE INVIERNO 🔥</h3>
                <ul>
                  <li>✅ Acceso completo al gimnasio</li>
                  <li>✅ Clases grupales incluidas</li>
                  <li>✅ Asesoramiento nutricional</li>
                  <li>✅ Plan de entrenamiento personalizado</li>
                </ul>
                
                <div className="winter-popup-validity">
                  <strong>⏰ Oferta válida hasta fin de mes</strong>
                </div>
              </div>
            </div>

            <div className="winter-popup-cta">
              <button className="winter-popup-btn-primary">
                ¡QUIERO MI DESCUENTO!
              </button>
              <p className="winter-popup-note">
                Presiona <kbd>X</kbd> para cerrar este popup
              </p>
            </div>
          </div>
        </div>

        {/* Efectos de nieve */}
        <div className="snowflakes">
          <div className="snowflake">❄</div>
          <div className="snowflake">❅</div>
          <div className="snowflake">❆</div>
          <div className="snowflake">❄</div>
          <div className="snowflake">❅</div>
          <div className="snowflake">❆</div>
        </div>
      </div>
    </div>
  );
};

export default WinterPromoPopup;

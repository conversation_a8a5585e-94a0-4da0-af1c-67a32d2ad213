/* Paleta de colores: <PERSON>, Na<PERSON><PERSON>, R<PERSON>jo, Amarillo */
:root {
  --color-black: #1a1a1a;
  --color-orange: #ff6600;
  --color-red: #dc2626;
  --color-yellow: #fbbf24;
  --color-white: #ffffff;
  --color-gray: #f5f5f5;
}

.footer {
  background: linear-gradient(135deg, var(--color-black) 0%, #2d2d2d 100%);
  color: var(--color-white);
  display: flex;
  justify-content: space-between;
  padding: 60px 80px;
  flex-wrap: wrap;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-orange) 0%, var(--color-red) 50%, var(--color-yellow) 100%);
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-width: 200px;
}

.footer-logo {
  width: 80px;
  height: auto;
}

.footer-text-logo {
  width: 120px;
  height: auto;
  margin-top: -10px;
}

.footer-email {
  color: #ccc;
  font-size: 14px;
}

.footer-title {
  font-weight: 700;
  font-size: 1.3rem;
  margin-bottom: 20px;
  color: #ecf0f1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-title i {
  color: var(--color-orange);
  font-size: 1.2rem;
}

.footer-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-list li {
  margin-bottom: 8px;
}

.footer-link {
  color: #bdc3c7;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  transition: all 0.3s ease;
  font-weight: 500;
}

.footer-link:hover {
  color: var(--color-orange);
  text-decoration: none;
  transform: translateX(5px);
}

.footer-link i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.footer-link:hover i {
  transform: scale(1.2);
}

.footer-button {
  background: linear-gradient(135deg, var(--color-orange) 0%, var(--color-red) 100%);
  color: var(--color-white);
  padding: 15px 25px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(255, 102, 0, 0.4);
  text-transform: uppercase;
  letter-spacing: 1px;
  border: 2px solid transparent;
}

.footer-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(255, 102, 0, 0.5);
  background: linear-gradient(135deg, var(--color-red) 0%, var(--color-orange) 100%);
  border-color: var(--color-yellow);
}

.footer-button i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.footer-button:hover i {
  transform: rotate(15deg);
}


@media (max-width: 800px) {
  .footer {
    flex-direction: column; 
    align-items: center;
    padding: 20px;
  }

  .footer-column {
    width: 100%; 
    align-items: center; 
    text-align: center; 
    margin-bottom: 20px; 
  }

  .footer-logo {
    margin-bottom: 10px; 
  }

  .footer-list {
    margin-bottom: 15px; 
  }

  .footer-button {
    width: auto; 
  }
}

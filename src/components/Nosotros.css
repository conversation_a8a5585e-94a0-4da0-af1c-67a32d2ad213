/* Paleta de colores: <PERSON>, Na<PERSON>ja, R<PERSON>jo, Amarillo */
:root {
  --color-black: #1a1a1a;
  --color-orange: #ff6600;
  --color-red: #dc2626;
  --color-yellow: #fbbf24;
  --color-white: #ffffff;
  --color-gray: #f5f5f5;
}

.nosotros-section {
  background: linear-gradient(135deg, var(--color-black) 0%, #2d2d2d 100%);
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.nosotros-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-orange) 0%, var(--color-red) 50%, var(--color-yellow) 100%);
}

.nosotros-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--color-white);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  text-transform: uppercase;
  letter-spacing: 3px;
}

.main-title i {
  color: var(--color-orange);
  font-size: 3rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.title-underline {
  width: 150px;
  height: 4px;
  background: linear-gradient(90deg, var(--color-orange) 0%, var(--color-red) 50%, var(--color-yellow) 100%);
  margin: 0 auto;
  border-radius: 2px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.info-card {
  background: linear-gradient(135deg, rgba(255, 102, 0, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  padding: 30px;
  border-radius: 15px;
  border-left: 5px solid var(--color-orange);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 102, 0, 0.1), transparent);
  transition: left 0.5s ease;
}

.info-card:hover::before {
  left: 100%;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(255, 102, 0, 0.2);
  border-left-color: var(--color-red);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-orange) 0%, var(--color-red) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  box-shadow: 0 8px 20px rgba(255, 102, 0, 0.3);
}

.card-icon i {
  color: var(--color-white);
  font-size: 1.5rem;
}

.info-card h3 {
  color: var(--color-white);
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.info-card p {
  color: #cccccc;
  font-size: 1.1rem;
  line-height: 1.7;
  margin: 0;
}

.info-card strong {
  color: var(--color-yellow);
  font-weight: 700;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
  padding: 25px;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(255, 102, 0, 0.1) 100%);
  border-radius: 15px;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: scale(1.05);
  border-color: var(--color-yellow);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--color-yellow);
  margin-bottom: 10px;
}

.stat-label {
  color: var(--color-white);
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Estilos para la sección de ubicación */
.location-content {
  position: relative;
}

.location-header {
  text-align: center;
  margin-bottom: 40px;
}

.location-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.location-title i {
  color: var(--color-red);
  font-size: 2.2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.location-subtitle {
  color: #cccccc;
  font-size: 1.2rem;
  margin: 0;
  font-style: italic;
}

.map-wrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  height: 500px;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
  transition: transform 0.3s ease;
}

.map-container:hover {
  transform: scale(1.02);
}

.map-container iframe {
  width: 100%;
  height: 100%;
  border: none;
  filter: grayscale(20%) contrast(1.2);
  transition: filter 0.3s ease;
}

.map-container:hover iframe {
  filter: grayscale(0%) contrast(1.3);
}

.map-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(45, 45, 45, 0.95) 100%);
  backdrop-filter: blur(10px);
  padding: 25px;
  border-radius: 15px;
  border: 2px solid var(--color-orange);
  max-width: 280px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.address-info h4 {
  color: var(--color-orange);
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.address-info h4 i {
  color: var(--color-yellow);
  font-size: 1rem;
}

.address-info p {
  color: var(--color-white);
  font-size: 0.95rem;
  margin-bottom: 15px;
  line-height: 1.5;
}

.address-info p:last-child {
  margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .main-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 15px;
  }

  .stats-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .nosotros-container {
    padding: 0 20px;
  }

  .main-title {
    font-size: 2rem;
  }

  .location-title {
    font-size: 1.8rem;
    flex-direction: column;
    gap: 10px;
  }

  .stats-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .map-overlay {
    position: static;
    margin-top: 20px;
    max-width: none;
  }

  .map-wrapper {
    height: 400px;
  }

  .info-card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.5rem;
  }

  .location-title {
    font-size: 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .info-card h3 {
    font-size: 1.2rem;
  }

  .info-card p {
    font-size: 1rem;
  }
}

/* Variables CSS */
:root {
  --color-black: #1a1a1a;
  --color-orange: #ff6600;
  --color-red: #dc2626;
  --color-yellow: #fbbf24;
  --color-white: #ffffff;
}

/* Sección Principal */
.profesores-section {
  background: linear-gradient(135deg, var(--color-black) 0%, #2d2d2d 100%);
  min-height: 100vh;
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.profesores-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 102, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.profesores-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* <PERSON><PERSON> de la Sección */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: 800;
  color: var(--color-white);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.section-title i {
  color: var(--color-orange);
  font-size: 2.5rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de Profesores - Desktop */
.profesores-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

/* Tarjeta de Profesor */
.profesor-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s ease;
  border: 2px solid rgba(255, 102, 0, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
}

.profesor-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(255, 102, 0, 0.3);
  border-color: var(--color-orange);
}

/* Header de la Tarjeta */
.card-header {
  position: relative;
  overflow: hidden;
}

.profesor-image-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.profesor-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.profesor-card:hover .profesor-image {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profesor-card:hover .image-overlay {
  opacity: 1;
}

.view-details-btn {
  background: linear-gradient(135deg, var(--color-orange) 0%, var(--color-red) 100%);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.view-details-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 102, 0, 0.4);
}

/* Cuerpo de la Tarjeta */
.card-body {
  padding: 30px;
  text-align: center;
}

.profesor-nombre {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.profesor-especialidad {
  font-size: 1rem;
  color: var(--color-orange);
  margin-bottom: 30px;
  font-weight: 600;
}





.horarios-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
}

.horarios-preview i {
  color: var(--color-orange);
}

/* Vista Móvil - Carrusel */
.mobile-carousel {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profesor-card-mobile {
  width: 100%;
  max-width: 400px;
  margin-bottom: 30px;
}

.carousel-indicators {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.indicator {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 2px solid var(--color-orange);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: var(--color-orange);
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(255, 102, 0, 0.6);
}

.indicator:hover {
  background: var(--color-yellow);
  border-color: var(--color-yellow);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: linear-gradient(145deg, var(--color-black) 0%, #2d2d2d 100%);
  border-radius: 20px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 2px solid var(--color-orange);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 102, 0, 0.2);
  border: 2px solid var(--color-orange);
  color: var(--color-orange);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  z-index: 1001;
}

.modal-close:hover {
  background: var(--color-orange);
  color: var(--color-white);
  transform: scale(1.1);
}

/* Header del Modal */
.modal-header {
  padding: 40px 40px 20px;
  border-bottom: 1px solid rgba(255, 102, 0, 0.3);
}

.profesor-info-header {
  display: flex;
  align-items: center;
  gap: 30px;
}

.modal-profesor-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--color-orange);
  box-shadow: 0 10px 30px rgba(255, 102, 0, 0.3);
}

.profesor-title-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.profesor-title-info .especialidad {
  font-size: 1.2rem;
  color: var(--color-orange);
  margin-bottom: 8px;
  font-weight: 600;
}



/* Cuerpo del Modal */
.modal-body {
  padding: 30px 40px 40px;
}

.modal-body h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--color-white);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.modal-body h3 i {
  color: var(--color-orange);
}

.descripcion-section {
  margin-bottom: 30px;
}

.descripcion-section p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 1rem;
}



.horarios-section {
  margin-bottom: 30px;
}

.horarios-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 15px;
  color: var(--color-white);
  font-size: 1.1rem;
  font-weight: 500;
  border: 1px solid rgba(255, 102, 0, 0.2);
}

.horarios-info i {
  color: var(--color-orange);
  font-size: 1.3rem;
}

.video-section {
  margin-bottom: 20px;
}

.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profesores-section {
    padding: 60px 0;
  }

  .section-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 10px;
  }

  .section-title i {
    font-size: 2rem;
  }

  .section-subtitle {
    font-size: 1rem;
    padding: 0 20px;
  }

  .profesores-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .profesor-card {
    margin: 0 10px;
  }

  .card-body {
    padding: 20px;
  }

  .profesor-nombre {
    font-size: 1.3rem;
  }



  /* Modal responsive */
  .modal-content {
    margin: 10px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 30px 20px 15px;
  }

  .profesor-info-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .modal-profesor-image {
    width: 100px;
    height: 100px;
  }

  .profesor-title-info h2 {
    font-size: 1.5rem;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-body h3 {
    font-size: 1.1rem;
  }



  .horarios-info {
    padding: 15px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.5rem;
  }

  .profesor-card {
    margin: 0 5px;
  }

  .modal-content {
    margin: 5px;
  }

  .modal-header {
    padding: 20px 15px 10px;
  }

  .modal-body {
    padding: 15px;
  }

  .profesor-title-info h2 {
    font-size: 1.3rem;
  }
}


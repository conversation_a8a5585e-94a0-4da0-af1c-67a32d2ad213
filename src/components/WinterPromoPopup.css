/* Overlay del popup */
.winter-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.5s ease-out;
}

/* Contenedor principal del popup */
.winter-popup-container {
  position: relative;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #87ceeb 100%);
  border-radius: 20px;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 0 3px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInScale 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* <PERSON><PERSON><PERSON> */
.winter-popup-close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 24px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10001;
  backdrop-filter: blur(10px);
}

.winter-popup-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Contenido del popup */
.winter-popup-content {
  padding: 30px;
  color: white;
  text-align: center;
}

/* Header del popup */
.winter-popup-header {
  margin-bottom: 25px;
}

.winter-popup-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
}

.winter-popup-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Cuerpo del popup */
.winter-popup-body {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.winter-popup-offer {
  display: flex;
  align-items: center;
  gap: 30px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Descuento */
.winter-popup-discount {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.discount-percentage {
  font-size: 3.5rem;
  font-weight: bold;
  color: #ffeb3b;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: pulse 1.5s ease-in-out infinite;
}

.discount-text {
  font-size: 1rem;
  font-weight: bold;
  color: #ffeb3b;
  margin-top: -10px;
}

/* Detalles de la oferta */
.winter-popup-details {
  flex: 1;
  text-align: left;
}

.winter-popup-details h3 {
  margin: 0 0 15px 0;
  font-size: 1.3rem;
  color: #ffeb3b;
}

.winter-popup-details ul {
  list-style: none;
  padding: 0;
  margin: 0 0 15px 0;
}

.winter-popup-details li {
  margin: 8px 0;
  font-size: 1rem;
}

.winter-popup-validity {
  background: rgba(255, 235, 59, 0.2);
  padding: 10px;
  border-radius: 8px;
  border-left: 4px solid #ffeb3b;
  color: #ffeb3b;
}

/* Call to action */
.winter-popup-cta {
  text-align: center;
}

.winter-popup-btn-primary {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border: none;
  padding: 15px 30px;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.winter-popup-btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(255, 107, 53, 0.6);
  background: linear-gradient(45deg, #ff8a65, #ffb74d);
}

.winter-popup-note {
  margin-top: 15px;
  font-size: 0.9rem;
  opacity: 0.8;
}

.winter-popup-note kbd {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
}

/* Efectos de nieve */
.snowflakes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.snowflake {
  position: absolute;
  top: -10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.5rem;
  animation: snowfall linear infinite;
}

.snowflake:nth-child(1) { left: 10%; animation-duration: 3s; animation-delay: 0s; }
.snowflake:nth-child(2) { left: 20%; animation-duration: 4s; animation-delay: 1s; }
.snowflake:nth-child(3) { left: 30%; animation-duration: 3.5s; animation-delay: 0.5s; }
.snowflake:nth-child(4) { left: 50%; animation-duration: 5s; animation-delay: 1.8s; }
.snowflake:nth-child(5) { left: 70%; animation-duration: 3.2s; animation-delay: 1s; }
.snowflake:nth-child(6) { left: 90%; animation-duration: 4.5s; animation-delay: 0.3s; }

/* Animaciones */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.5) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes glow {
  from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 10px rgba(255, 255, 255, 0.3); }
  to { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.6); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes snowfall {
  from {
    transform: translateY(-100px) rotate(0deg);
    opacity: 1;
  }
  to {
    transform: translateY(calc(100vh + 100px)) rotate(360deg);
    opacity: 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .winter-popup-container {
    width: 95%;
    margin: 20px;
  }
  
  .winter-popup-content {
    padding: 20px;
  }
  
  .winter-popup-title {
    font-size: 2rem;
  }
  
  .winter-popup-offer {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .winter-popup-details {
    text-align: center;
  }
  
  .discount-percentage {
    font-size: 2.5rem;
  }
}

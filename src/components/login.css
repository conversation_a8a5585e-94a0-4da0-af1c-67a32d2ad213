/* Paleta de colores: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Amarillo */
:root {
  --color-black: #1a1a1a;
  --color-orange: #ff6600;
  --color-red: #dc2626;
  --color-yellow: #fbbf24;
  --color-white: #ffffff;
  --color-gray: #f5f5f5;
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, var(--color-black) 0%, #2d2d2d 100%);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('../assets/login.jpg') no-repeat center center;
  background-size: cover;
  opacity: 0.1;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.login-form {
  background: r<PERSON><PERSON>(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 50px 40px;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-orange) 0%, var(--color-red) 50%, var(--color-yellow) 100%);
  border-radius: 20px 20px 0 0;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header i {
  font-size: 3rem;
  color: var(--color-orange);
  margin-bottom: 15px;
  display: block;
}

.login-header h2 {
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
  font-size: 2rem;
  background: linear-gradient(135deg, var(--color-orange) 0%, var(--color-red) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-transform: uppercase;
  letter-spacing: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.login-header p {
  color: #666;
  font-size: 1rem;
  margin: 0;
  font-style: italic;
}

.input-group {
  margin-bottom: 25px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper i {
  position: absolute;
  left: 18px;
  color: var(--color-orange);
  font-size: 1.1rem;
  z-index: 1;
}

.login-form input[type="text"],
.login-form input[type="password"] {
  width: 100%;
  padding: 18px 20px 18px 50px;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  font-weight: 500;
}

.login-form input[type="text"]:focus,
.login-form input[type="password"]:focus {
  outline: none;
  border-color: var(--color-orange);
  box-shadow: 0 0 20px rgba(255, 102, 0, 0.3);
  background: white;
  transform: translateY(-2px);
}

.login-form input[type="text"]::placeholder,
.login-form input[type="password"]::placeholder {
  color: #999;
  font-weight: 400;
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, var(--color-orange) 0%, var(--color-red) 100%);
  color: var(--color-white);
  padding: 18px 30px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(255, 102, 0, 0.4);
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.login-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(255, 102, 0, 0.5);
  background: linear-gradient(135deg, var(--color-red) 0%, var(--color-orange) 100%);
  border-color: var(--color-yellow);
}

.login-button:active {
  transform: translateY(-1px);
}

.login-button i {
  font-size: 1.2rem;
}



.error-message {
  color: var(--color-red);
  margin-bottom: 20px;
  text-align: center;
  font-size: 1rem;
  font-weight: 500;
  background: rgba(220, 38, 38, 0.1);
  padding: 12px 20px;
  border-radius: 8px;
  border-left: 4px solid var(--color-red);
  border: 1px solid rgba(220, 38, 38, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.error-message i {
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-form {
    padding: 30px 25px;
    margin: 20px;
  }

  .login-header h2 {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }

  .login-form input[type="text"],
  .login-form input[type="password"] {
    padding: 15px 18px;
    font-size: 1rem;
  }

  .login-form button[type="submit"] {
    padding: 15px 25px;
    font-size: 1rem;
  }
}